/**
 * Payment Module
 *
 * This module encapsulates all payment-related functionality for the Career Ireland platform.
 * It provides a complete payment processing system with Stripe integration, supporting
 * multiple service types and both authenticated and guest user flows.
 *
 * Module Components:
 * - PaymentController: REST API endpoints for payment operations
 * - PaymentService: Core payment processing logic and Stripe integration
 * - StripeProvider: Stripe client configuration and initialization
 * - PrismaService: Database operations for payment records
 * - JwtService: JWT token handling for authenticated users
 * - MailerService: Email notifications for payment confirmations
 *
 * Features Provided:
 * - Stripe Checkout Session creation
 * - Webhook handling for payment confirmations
 * - Email notifications (customer and admin)
 * - Payment history tracking
 * - Support for multiple service types (mentor, package, immigration, training)
 * - Guest and authenticated user payment flows
 *
 * Dependencies:
 * - Stripe: Payment processing
 * - Prisma: Database ORM
 * - NestJS JWT: Authentication
 * - Custom Mailer: Email notifications
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */

import { Module } from '@nestjs/common';
import { PaymentController } from './payment.controller';
import { PaymentService } from './payment.service';
import { StripeProvider } from 'src/config/stripe.config';
import { PrismaService } from 'src/utils/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { MailerService } from 'src/mailer/mailer.service';

@Module({
  controllers: [PaymentController],
  providers: [
    PaymentService,
    StripeProvider,
    PrismaService,
    JwtService,
    MailerService,
  ],
})
export class PaymentModule {}
