/**
 * Unified Payment Service
 *
 * This service provides a unified approach to handling payments across all service types
 * in the Career Ireland platform. It consolidates the previously separate payment tables
 * into a single payment table with proper service type differentiation.
 *
 * Key Features:
 * - Single payment table for all service types
 * - Support for both authenticated users and guests
 * - Unified payment creation and management
 * - Consistent email notification system
 * - Advanced filtering and pagination
 * - Payment status tracking and updates
 * - Revenue analytics and reporting
 *
 * Service Types Supported:
 * - mentor: Career consultation and mentorship services
 * - package: Bundled service offerings
 * - immigration: Visa and immigration consultation services
 * - training: Skill development and training programs
 *
 * Payment Types:
 * - user: Payments by authenticated users
 * - guest: Payments by non-authenticated users
 *
 * This service represents the new architecture for payment handling and will
 * eventually replace the legacy payment service methods.
 *
 * <AUTHOR> Ireland Development Team
 * @version 2.0.0
 * @since 2025-01-15
 */

import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/utils/prisma.service';
import { MailerService } from 'src/mailer/mailer.service';
import { render } from '@react-email/components';
import MentorPaymentSuccessEmail from 'src/template/service';
import PurchaseNotificationEmail from 'src/template/purchase-notification';

/**
 * Interface for creating unified payment records
 *
 * This interface defines the structure for payment data that can be used
 * across all service types. Only one of the service reference fields
 * (serviceId, packageId, immigration_serviceId, trainingId) should be populated
 * based on the service_type.
 */
interface CreatePaymentData {
  amount: number;
  status: string;
  payment_type: 'user' | 'guest';
  service_type: 'mentor' | 'package' | 'immigration' | 'training';
  progress?: string;

  // User fields
  userId?: string;

  // Guest fields
  guest_name?: string;
  guest_email?: string;
  guest_mobile?: string;

  // Service references (only one should be populated)
  serviceId?: string;
  packageId?: string;
  immigration_serviceId?: string;
  trainingId?: string;

  // Stripe information
  stripe_session_id?: string;
  stripe_payment_intent_id?: string;
}

interface PaymentQueryOptions {
  page?: number;
  limit?: number;
  payment_type?: 'user' | 'guest';
  service_type?: 'mentor' | 'package' | 'immigration' | 'training';
  status?: string;
  userId?: string;
}

@Injectable()
export class UnifiedPaymentService {
  constructor(
    private prisma: PrismaService,
    private mailer: MailerService,
  ) {}

  /**
   * Create a unified payment record
   */
  async createPayment(data: CreatePaymentData) {
    const payment = await this.prisma.payment.create({
      data: {
        ...data,
        progress: data.progress || 'Pending',
      },
      include: this.getIncludeOptions(data.service_type),
    });

    // Send email notifications
    await this.sendEmailNotifications(payment);

    return payment;
  }

  /**
   * Get payments with filtering and pagination
   */
  async getPayments(options: PaymentQueryOptions = {}) {
    const {
      page = 1,
      limit = 10,
      payment_type,
      service_type,
      status,
      userId,
    } = options;

    const isPagination = page > 0 && limit > 0;
    const where: any = {};

    if (payment_type) where.payment_type = payment_type;
    if (service_type) where.service_type = service_type;
    if (status) where.status = status;
    if (userId) where.userId = userId;

    const payments = await this.prisma.payment.findMany({
      where,
      skip: isPagination ? (page - 1) * limit : undefined,
      take: isPagination ? limit : undefined,
      orderBy: { createdAt: 'desc' },
      include: {
        user: true,
        service: {
          include: { mentor: true },
        },
        package: true,
        immigration_service: true,
        training: true,
      },
    });

    const total = await this.prisma.payment.count({ where });

    return {
      data: payments,
      pagination: isPagination
        ? {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
          }
        : null,
    };
  }

  /**
   * Get payment by ID
   */
  async getPaymentById(id: string) {
    return await this.prisma.payment.findUnique({
      where: { id },
      include: {
        user: true,
        service: {
          include: { mentor: true },
        },
        package: true,
        immigration_service: true,
        training: true,
      },
    });
  }

  /**
   * Update payment status
   */
  async updatePaymentStatus(id: string, status: string, progress?: string) {
    const updateData: any = { status };
    if (progress) updateData.progress = progress;

    return await this.prisma.payment.update({
      where: { id },
      data: updateData,
      include: this.getFullIncludeOptions(),
    });
  }

  /**
   * Get revenue statistics
   */
  async getRevenueStats() {
    const stats = await this.prisma.payment.groupBy({
      by: ['service_type', 'payment_type'],
      where: { status: 'paid' },
      _sum: { amount: true },
      _count: { id: true },
    });

    const totalRevenue = await this.prisma.payment.aggregate({
      where: { status: 'paid' },
      _sum: { amount: true },
      _count: { id: true },
    });

    return {
      total_revenue: totalRevenue._sum.amount || 0,
      total_transactions: totalRevenue._count || 0,
      breakdown: stats.map((stat) => ({
        service_type: stat.service_type,
        payment_type: stat.payment_type,
        revenue: stat._sum.amount || 0,
        transactions: stat._count || 0,
      })),
    };
  }

  /**
   * Get user's total spending
   */
  async getUserTotalSpent(userId: string) {
    const result = await this.prisma.payment.aggregate({
      where: {
        userId,
        payment_type: 'user',
        status: 'paid',
      },
      _sum: { amount: true },
    });

    return result._sum.amount || 0;
  }

  /**
   * Get mentor revenue
   */
  async getMentorRevenue(mentorId: string) {
    const result = await this.prisma.payment.aggregate({
      where: {
        service_type: 'mentor',
        status: 'paid',
        service: {
          mentorId,
        },
      },
      _sum: { amount: true },
      _count: { id: true },
    });

    const uniqueClients = await this.prisma.payment.findMany({
      where: {
        service_type: 'mentor',
        status: 'paid',
        payment_type: 'user',
        service: {
          mentorId,
        },
      },
      select: { userId: true },
      distinct: ['userId'],
    });

    return {
      revenue: result._sum.amount || 0,
      transactions: result._count || 0,
      unique_clients: uniqueClients.length,
    };
  }

  /**
   * Transform payment to legacy format for backward compatibility
   */
  transformToLegacyFormat(payment: any) {
    const baseResponse = {
      id: payment.id,
      amount: payment.amount,
      status: payment.status,
      progress: payment.progress,
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt,
    };

    // Add user or guest fields
    if (payment.payment_type === 'user') {
      baseResponse.userId = payment.userId;
    } else {
      baseResponse.name = payment.guest_name;
      baseResponse.email = payment.guest_email;
      baseResponse.mobile_no = payment.guest_mobile;
    }

    // Add service-specific fields and relations
    switch (payment.service_type) {
      case 'mentor':
        return {
          ...baseResponse,
          serviceId: payment.serviceId,
          mentor_services: payment.service,
        };
      case 'package':
        return {
          ...baseResponse,
          packageId: payment.packageId,
          package: payment.package,
        };
      case 'immigration':
        return {
          ...baseResponse,
          immigration_serviceId: payment.immigration_serviceId,
          immigration_service: payment.immigration_service,
        };
      case 'training':
        return {
          ...baseResponse,
          trainingId: payment.trainingId,
          training: payment.training,
        };
      default:
        return baseResponse;
    }
  }

  /**
   * Get include options based on service type
   */
  private getIncludeOptions(serviceType: string) {
    const baseInclude = { user: true };

    switch (serviceType) {
      case 'mentor':
        return {
          ...baseInclude,
          service: {
            include: { mentor: true },
          },
        };
      case 'package':
        return {
          ...baseInclude,
          package: true,
        };
      case 'immigration':
        return {
          ...baseInclude,
          immigration_service: true,
        };
      case 'training':
        return {
          ...baseInclude,
          training: true,
        };
      default:
        return baseInclude;
    }
  }

  /**
   * Get full include options for all relations
   */
  private getFullIncludeOptions() {
    return {
      user: true,
      service: {
        include: { mentor: true },
      },
      package: true,
      immigration_service: true,
      training: true,
    };
  }

  /**
   * Send email notifications for payment
   */
  private async sendEmailNotifications(payment: any) {
    try {
      // Send confirmation email to customer
      const customerEmail =
        payment.payment_type === 'user'
          ? payment.user?.email
          : payment.guest_email;

      const customerName =
        payment.payment_type === 'user'
          ? payment.user?.name
          : payment.guest_name;

      if (customerEmail && customerName) {
        await this.sendCustomerConfirmation(
          payment,
          customerEmail,
          customerName,
        );
      }

      // Send admin notification
      await this.sendAdminNotification(payment);
    } catch (error) {
      console.error('Failed to send email notifications:', error);
      // Don't throw error to avoid breaking payment flow
    }
  }

  /**
   * Send customer confirmation email
   */
  private async sendCustomerConfirmation(
    payment: any,
    email: string,
    name: string,
  ) {
    const serviceName = this.getServiceName(payment);

    await this.mailer.sendEmail({
      from: process.env.EMAIL,
      to: email,
      subject: `Payment Confirmation - ${serviceName}`,
      html: await render(
        MentorPaymentSuccessEmail({
          service: {
            ...payment,
            name: serviceName,
          },
          user: { email, name },
        }),
      ),
    });
  }

  /**
   * Send admin notification email
   */
  private async sendAdminNotification(payment: any) {
    const serviceName = this.getServiceName(payment);

    await this.mailer.sendEmail({
      from: process.env.EMAIL,
      to: process.env.EMAIL,
      subject: `New Purchase - ${serviceName}`,
      html: await render(
        PurchaseNotificationEmail({
          name: serviceName,
          service: payment,
          user: {
            email:
              payment.payment_type === 'user'
                ? payment.user?.email
                : payment.guest_email,
            name:
              payment.payment_type === 'user'
                ? payment.user?.name
                : payment.guest_name,
            mobile_no:
              payment.payment_type === 'guest' ? payment.guest_mobile : '',
          },
        }),
      ),
    });
  }

  /**
   * Get service name based on payment data
   */
  private getServiceName(payment: any): string {
    switch (payment.service_type) {
      case 'mentor':
        return payment.service?.name || 'Mentor Service';
      case 'package':
        return payment.package?.name || 'Service Package';
      case 'immigration':
        return payment.immigration_service?.name || 'Immigration Service';
      case 'training':
        return payment.training?.name || 'Training Program';
      default:
        return 'Service';
    }
  }
}
