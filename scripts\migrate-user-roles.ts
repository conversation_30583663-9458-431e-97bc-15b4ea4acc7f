import { PrismaClient } from '@prisma/client';

// NOTE: This script requires the Prisma schema to be updated with the unified_user table
// Run `npx prisma generate` after updating the schema and before running this migration
const prisma = new PrismaClient();

interface MigrationStats {
  regularUsers: number;
  adminUsers: number;
  mentorUsers: number;
  agentUsers: number;
  total: number;
  duplicateEmails: string[];
  errors: string[];
}

enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
  MENTOR = 'mentor',
  AGENT = 'agent',
  SUPER_ADMIN = 'super_admin',
}

enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
  DELETED = 'deleted',
}

async function migrateUserRoles(): Promise<MigrationStats> {
  console.log('🚀 Starting user role migration...');

  const stats: MigrationStats = {
    regularUsers: 0,
    adminUsers: 0,
    mentorUsers: 0,
    agentUsers: 0,
    total: 0,
    duplicateEmails: [],
    errors: [],
  };

  try {
    // Check for email conflicts across tables
    await checkEmailConflicts(stats);

    if (stats.duplicateEmails.length > 0) {
      console.error(
        '❌ Email conflicts detected. Please resolve before migration:',
      );
      stats.duplicateEmails.forEach((email) => console.error(`  - ${email}`));
      throw new Error('Email conflicts must be resolved before migration');
    }

    // Start transaction for data consistency
    await prisma.$transaction(async (tx) => {
      // Migrate regular users
      stats.regularUsers = await migrateRegularUsers(tx, stats);

      // Migrate admin users
      stats.adminUsers = await migrateAdminUsers(tx, stats);

      // Migrate mentor users
      stats.mentorUsers = await migrateMentorUsers(tx, stats);

      // Create sample agent users (no existing table to migrate from)
      stats.agentUsers = await createSampleAgentUsers(tx, stats);
    });

    stats.total =
      stats.regularUsers +
      stats.adminUsers +
      stats.mentorUsers +
      stats.agentUsers;

    console.log('✅ User role migration completed successfully!');
    console.log('📊 Migration Statistics:', stats);

    return stats;
  } catch (error) {
    console.error('❌ User role migration failed:', error);
    stats.errors.push(error.message);
    throw error;
  }
}

async function checkEmailConflicts(stats: MigrationStats): Promise<void> {
  console.log('🔍 Checking for email conflicts...');

  // Get all emails from all tables
  const userEmails = await prisma.user.findMany({ select: { email: true } });
  const adminEmails = await prisma.admin.findMany({ select: { email: true } });
  const mentorEmails = await prisma.mentor.findMany({
    select: { email: true },
  });

  const allEmails = [
    ...userEmails.map((u) => ({ email: u.email, source: 'user' })),
    ...adminEmails.map((a) => ({ email: a.email, source: 'admin' })),
    ...mentorEmails.map((m) => ({ email: m.email, source: 'mentor' })),
  ];

  // Find duplicates
  const emailCounts = new Map<string, string[]>();
  allEmails.forEach(({ email, source }) => {
    if (!emailCounts.has(email)) {
      emailCounts.set(email, []);
    }
    emailCounts.get(email)!.push(source);
  });

  emailCounts.forEach((sources, email) => {
    if (sources.length > 1) {
      stats.duplicateEmails.push(`${email} (found in: ${sources.join(', ')})`);
    }
  });

  if (stats.duplicateEmails.length === 0) {
    console.log('✅ No email conflicts detected');
  }
}

async function migrateRegularUsers(
  tx: any,
  stats: MigrationStats,
): Promise<number> {
  console.log('📦 Migrating regular users...');

  const users = await tx.user.findMany();

  for (const user of users) {
    try {
      await tx.unified_user.create({
        data: {
          id: user.id,
          name: user.name,
          email: user.email,
          emailVerified: user.emailVerified || false,
          image: user.image,
          password: user.password,
          role: UserRole.USER,
          status: UserStatus.ACTIVE,
          provider: user.provider?.toLowerCase() || 'credentials',
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          permissions: [
            'purchase_services',
            'leave_reviews',
            'access_dashboard',
            'manage_profile',
          ],
          metadata: {
            migrated_from: 'user_table',
            original_provider: user.provider,
            migration_date: new Date().toISOString(),
          },
          login_count: 0,
        },
      });
    } catch (error) {
      console.error(`❌ Failed to migrate user ${user.email}:`, error.message);
      stats.errors.push(`User ${user.email}: ${error.message}`);
    }
  }

  console.log(`✅ Migrated ${users.length} regular users`);
  return users.length;
}

async function migrateAdminUsers(
  tx: any,
  stats: MigrationStats,
): Promise<number> {
  console.log('📦 Migrating admin users...');

  const admins = await tx.admin.findMany();

  for (const admin of admins) {
    try {
      await tx.unified_user.create({
        data: {
          id: admin.id,
          name: admin.name,
          email: admin.email,
          emailVerified: admin.emailVerified || true, // Admins are typically verified
          image: admin.image,
          password: admin.password,
          role: UserRole.ADMIN,
          status: UserStatus.ACTIVE,
          provider: 'credentials',
          createdAt: admin.createdAt,
          updatedAt: admin.updatedAt,
          permissions: [
            'manage_users',
            'manage_mentors',
            'manage_services',
            'manage_payments',
            'view_analytics',
            'manage_content',
            'system_administration',
          ],
          metadata: {
            migrated_from: 'admin_table',
            migration_date: new Date().toISOString(),
            admin_level: 'standard',
          },
          login_count: 0,
        },
      });
    } catch (error) {
      console.error(
        `❌ Failed to migrate admin ${admin.email}:`,
        error.message,
      );
      stats.errors.push(`Admin ${admin.email}: ${error.message}`);
    }
  }

  console.log(`✅ Migrated ${admins.length} admin users`);
  return admins.length;
}

async function migrateMentorUsers(
  tx: any,
  stats: MigrationStats,
): Promise<number> {
  console.log('📦 Migrating mentor users...');

  const mentors = await tx.mentor.findMany();

  for (const mentor of mentors) {
    try {
      // Determine status based on mentor status
      let status = UserStatus.ACTIVE;
      if (mentor.status) {
        switch (mentor.status.toLowerCase()) {
          case 'active':
          case 'accepted':
            status = UserStatus.ACTIVE;
            break;
          case 'pending':
            status = UserStatus.PENDING;
            break;
          case 'inactive':
            status = UserStatus.INACTIVE;
            break;
          case 'suspended':
            status = UserStatus.SUSPENDED;
            break;
          default:
            status = UserStatus.ACTIVE;
        }
      }

      await tx.unified_user.create({
        data: {
          id: mentor.id,
          name: mentor.name,
          email: mentor.email,
          emailVerified: mentor.emailVerified || false,
          image: mentor.image,
          password: mentor.password,
          role: UserRole.MENTOR,
          status: status,
          provider: 'credentials',
          location: mentor.location,
          designation: mentor.designation,
          description: mentor.desc,
          display_order: mentor.order,
          linkedin_url: mentor.linkedin,
          profile_url: mentor.profile,
          createdAt: mentor.createdAt,
          updatedAt: mentor.updatedAt,
          permissions: [
            'manage_own_services',
            'view_own_analytics',
            'respond_to_reviews',
            'manage_profile',
            'view_bookings',
          ],
          metadata: {
            migrated_from: 'mentor_table',
            original_status: mentor.status,
            services_id: mentor.servicesId,
            migration_date: new Date().toISOString(),
          },
          login_count: 0,
        },
      });
    } catch (error) {
      console.error(
        `❌ Failed to migrate mentor ${mentor.email}:`,
        error.message,
      );
      stats.errors.push(`Mentor ${mentor.email}: ${error.message}`);
    }
  }

  console.log(`✅ Migrated ${mentors.length} mentor users`);
  return mentors.length;
}

async function createSampleAgentUsers(
  tx: any,
  stats: MigrationStats,
): Promise<number> {
  console.log('📦 Creating sample agent users...');

  const sampleAgents = [
    {
      name: "Sarah O'Connor",
      email: '<EMAIL>',
      license_number: 'ICCRC-2024-001',
      specializations: [
        'Family Reunification',
        'Work Permits',
        'Student Visas',
      ],
      years_experience: 8,
      consultation_fee: 15000, // €150 in cents
      languages: ['English', 'Irish', 'French'],
      office_address: 'Dublin 2, Ireland',
      certification_body: 'ICCRC',
      license_expiry: '2025-12-31',
    },
    {
      name: 'Michael Chen',
      email: '<EMAIL>',
      license_number: 'LSI-2024-002',
      specializations: [
        'Business Immigration',
        'Investment Visas',
        'EU Blue Card',
      ],
      years_experience: 12,
      consultation_fee: 20000, // €200 in cents
      languages: ['English', 'Mandarin', 'Cantonese'],
      office_address: 'Cork, Ireland',
      certification_body: 'Law Society of Ireland',
      license_expiry: '2026-06-30',
    },
  ];

  const { hash } = await import('bcrypt');

  for (const agent of sampleAgents) {
    try {
      await tx.unified_user.create({
        data: {
          name: agent.name,
          email: agent.email,
          emailVerified: true,
          password: await hash('TempPassword123!', 10), // Temporary password
          role: UserRole.AGENT,
          status: UserStatus.ACTIVE,
          provider: 'credentials',
          license_number: agent.license_number,
          specializations: agent.specializations,
          years_experience: agent.years_experience,
          consultation_fee: agent.consultation_fee,
          languages: agent.languages,
          office_address: agent.office_address,
          certification_body: agent.certification_body,
          license_expiry: new Date(agent.license_expiry),
          permissions: [
            'manage_immigration_services',
            'view_client_cases',
            'process_applications',
            'generate_reports',
            'manage_consultations',
            'manage_profile',
          ],
          metadata: {
            created_during_migration: true,
            user_type: 'sample_agent',
            creation_date: new Date().toISOString(),
          },
          login_count: 0,
        },
      });
    } catch (error) {
      console.error(`❌ Failed to create agent ${agent.email}:`, error.message);
      stats.errors.push(`Agent ${agent.email}: ${error.message}`);
    }
  }

  console.log(`✅ Created ${sampleAgents.length} sample agent users`);
  return sampleAgents.length;
}

// Validation function to ensure migration integrity
async function validateUserMigration(): Promise<boolean> {
  console.log('🔍 Validating user migration...');

  try {
    const oldTableCounts = await Promise.all([
      prisma.user.count(),
      prisma.admin.count(),
      prisma.mentor.count(),
    ]);

    const totalOldRecords = oldTableCounts.reduce(
      (sum, count) => sum + count,
      0,
    );
    const newTableCount = await prisma.unified_user.count();

    if (totalOldRecords !== newTableCount) {
      console.error(
        `❌ Validation failed: ${totalOldRecords} old records vs ${newTableCount} new records`,
      );
      return false;
    }

    // Validate role distribution
    const roleCounts = await prisma.unified_user.groupBy({
      by: ['role'],
      _count: { id: true },
    });

    console.log('📊 Role distribution:');
    roleCounts.forEach(({ role, _count }) => {
      console.log(`  ${role}: ${_count.id} users`);
    });

    // Validate email uniqueness
    const emailCount = await prisma.unified_user.count();
    const uniqueEmailCount = await prisma.unified_user.count({
      distinct: ['email'],
    });

    if (emailCount !== uniqueEmailCount) {
      console.error(
        `❌ Email uniqueness validation failed: ${emailCount} total vs ${uniqueEmailCount} unique emails`,
      );
      return false;
    }

    // Validate mentor-specific fields
    const mentorUsers = await prisma.unified_user.findMany({
      where: { role: UserRole.MENTOR },
      select: { id: true, designation: true, description: true },
    });

    const mentorsWithoutRequiredFields = mentorUsers.filter(
      (mentor) => !mentor.designation || !mentor.description,
    );

    if (mentorsWithoutRequiredFields.length > 0) {
      console.error(
        `❌ Found ${mentorsWithoutRequiredFields.length} mentors without required fields`,
      );
      return false;
    }

    console.log('✅ User migration validation passed!');
    return true;
  } catch (error) {
    console.error('❌ Validation error:', error);
    return false;
  }
}

// Function to create sample super admin
async function createSuperAdmin(): Promise<void> {
  console.log('👑 Creating super admin user...');

  const superAdminEmail =
    process.env.SUPER_ADMIN_EMAIL || '<EMAIL>';
  const superAdminPassword =
    process.env.SUPER_ADMIN_PASSWORD || 'SuperAdmin123!';

  try {
    const existingSuperAdmin = await prisma.unified_user.findFirst({
      where: { role: UserRole.SUPER_ADMIN },
    });

    if (existingSuperAdmin) {
      console.log('✅ Super admin already exists');
      return;
    }

    const { hash } = await import('bcrypt');
    const hashedPassword = await hash(superAdminPassword, 10);

    await prisma.unified_user.create({
      data: {
        name: 'Super Administrator',
        email: superAdminEmail,
        emailVerified: true,
        password: hashedPassword,
        role: UserRole.SUPER_ADMIN,
        status: UserStatus.ACTIVE,
        provider: 'credentials',
        permissions: [
          'manage_users',
          'manage_mentors',
          'manage_services',
          'manage_payments',
          'view_analytics',
          'manage_content',
          'system_administration',
          'manage_admins',
          'manage_roles',
          'system_configuration',
        ],
        metadata: {
          created_during_migration: true,
          creation_date: new Date().toISOString(),
          admin_level: 'super',
        },
        login_count: 0,
      },
    });

    console.log(`✅ Super admin created with email: ${superAdminEmail}`);
    console.log(`🔑 Default password: ${superAdminPassword}`);
    console.log('⚠️  Please change the password after first login!');
  } catch (error) {
    console.error('❌ Failed to create super admin:', error);
  }
}

// Main execution
if (require.main === module) {
  migrateUserRoles()
    .then(async (stats) => {
      const isValid = await validateUserMigration();
      if (isValid && stats.errors.length === 0) {
        await createSuperAdmin();
        console.log(
          '🎉 User role migration completed successfully with validation!',
        );
        process.exit(0);
      } else {
        console.error(
          '💥 User role migration completed but validation failed or errors occurred!',
        );
        if (stats.errors.length > 0) {
          console.error('Errors encountered:');
          stats.errors.forEach((error) => console.error(`  - ${error}`));
        }
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('💥 User role migration failed:', error);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { migrateUserRoles, validateUserMigration, createSuperAdmin };
