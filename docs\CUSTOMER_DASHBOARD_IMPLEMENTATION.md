# Customer Dashboard Implementation Plan

## Overview

This document outlines the implementation strategy for the Customer Dashboard feature for the Career Ireland Immigration SaaS platform. The customer dashboard will provide authenticated users with the ability to track their immigration service applications, upload required documents, and monitor their application status through a defined workflow.

## Current State Analysis

### Existing Features
Based on the codebase analysis, the following features are already implemented:

1. **User Authentication System**
   - User registration and login (`/user/register`, `/user/login`)
   - JWT-based authentication with refresh tokens
   - Email verification system
   - Password reset functionality

2. **Payment System**
   - Immigration service purchases (`user_immigration_service` table)
   - Payment status tracking (`status`, `progress` fields)
   - Stripe integration for payment processing
   - Email notifications for purchases

3. **Admin Dashboard**
   - Admin-only dashboard (`/dashboard`) for viewing system metrics
   - Progress update functionality for admin users
   - Guest purchase management

4. **File Upload System**
   - Media upload service with Supabase storage
   - Support for multiple file types
   - File size limitations (25MB)

5. **Immigration Service Management**
   - Immigration service CRUD operations
   - Service array for multiple offerings
   - Pricing and ordering system

### Missing Features (To Be Implemented)

1. **Customer Dashboard Interface**
2. **Application Management System**
3. **Document Upload and Management**
4. **Workflow Management System**
5. **Document Requirements Definition**
6. **Application Status Tracking**
7. **Email Notification System for Customers**
8. **Application ID Generation**

## Target State Design

### Improved Database Schema Extensions

#### 1. Customer Application Table (Enhanced)
```sql
CREATE TABLE "customer_application" (
    "id" TEXT NOT NULL,
    "application_id" TEXT NOT NULL UNIQUE, -- Human-readable ID (e.g., CI-2024-001)
    "user_id" TEXT NOT NULL,
    "immigration_service_id" TEXT NOT NULL,
    "payment_id" TEXT NOT NULL, -- Reference to unified payment table
    "workflow_template_id" TEXT, -- Reference to workflow template (more flexible)
    "status" "ApplicationStatus" NOT NULL DEFAULT 'PENDING',
    "priority" "ApplicationPriority" NOT NULL DEFAULT 'NORMAL',
    "current_step" INTEGER NOT NULL DEFAULT 1,
    "total_steps" INTEGER NOT NULL,
    "estimated_completion_date" TIMESTAMP(3),
    "actual_completion_date" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "submitted_at" TIMESTAMP(3),
    "completed_at" TIMESTAMP(3),
    "notes" TEXT,
    "metadata" JSONB, -- For flexible additional data
    "assigned_agent_id" TEXT, -- For case assignment

    CONSTRAINT "customer_application_pkey" PRIMARY KEY ("id")
);

-- Enhanced Indexes
CREATE INDEX "customer_application_user_id_idx" ON "customer_application"("user_id");
CREATE INDEX "customer_application_status_idx" ON "customer_application"("status");
CREATE INDEX "customer_application_priority_idx" ON "customer_application"("priority");
CREATE INDEX "customer_application_application_id_idx" ON "customer_application"("application_id");
CREATE INDEX "customer_application_assigned_agent_idx" ON "customer_application"("assigned_agent_id");
CREATE INDEX "customer_application_created_at_idx" ON "customer_application"("created_at");
CREATE INDEX "customer_application_metadata_gin_idx" ON "customer_application" USING GIN ("metadata");
```

#### 1.1 Enhanced Application Status and Priority Enums
```sql
CREATE TYPE "ApplicationStatus" AS ENUM (
    'DRAFT',
    'PENDING',
    'DOCUMENT_COLLECTION',
    'UNDER_REVIEW',
    'ADDITIONAL_INFO_REQUIRED',
    'PROCESSING',
    'APPROVED',
    'REJECTED',
    'COMPLETED',
    'CANCELLED',
    'ON_HOLD'
);

CREATE TYPE "ApplicationPriority" AS ENUM (
    'LOW',
    'NORMAL',
    'HIGH',
    'URGENT'
);
```

#### 2. Workflow Template System (New - More Flexible)
```sql
CREATE TABLE "workflow_template" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "immigration_service_id" TEXT, -- Can be null for reusable templates
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "version" INTEGER NOT NULL DEFAULT 1,
    "created_by" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "workflow_template_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "workflow_step_template" (
    "id" TEXT NOT NULL,
    "workflow_template_id" TEXT NOT NULL,
    "step_number" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "step_type" "WorkflowStepType" NOT NULL,
    "estimated_duration_days" INTEGER,
    "is_customer_action_required" BOOLEAN NOT NULL DEFAULT false,
    "auto_complete_conditions" JSONB, -- Conditions for auto-completion
    "required_documents" TEXT[], -- Array of document requirement IDs
    "dependencies" TEXT[], -- Array of step IDs that must complete first
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "workflow_step_template_pkey" PRIMARY KEY ("id")
);

CREATE TYPE "WorkflowStepType" AS ENUM (
    'DOCUMENT_COLLECTION',
    'REVIEW',
    'APPROVAL',
    'PROCESSING',
    'NOTIFICATION',
    'WAITING',
    'CUSTOM'
);
```

#### 3. Enhanced Document Requirements Table
```sql
CREATE TABLE "document_requirement" (
    "id" TEXT NOT NULL,
    "immigration_service_id" TEXT, -- Can be null for reusable requirements
    "category_id" TEXT, -- Group related documents
    "name" TEXT NOT NULL,
    "description" TEXT,
    "instructions" TEXT, -- Detailed instructions for customers
    "is_required" BOOLEAN NOT NULL DEFAULT true,
    "file_types" TEXT[], -- ['pdf', 'jpg', 'png', 'docx']
    "max_file_size" INTEGER NOT NULL DEFAULT 25000000, -- 25MB in bytes
    "min_file_size" INTEGER DEFAULT 1024, -- Minimum file size
    "max_files_count" INTEGER DEFAULT 1, -- Allow multiple files
    "validation_rules" JSONB, -- Custom validation rules
    "order" INTEGER NOT NULL DEFAULT 0,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "expires_after_days" INTEGER, -- Document expiry
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "document_requirement_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "document_category" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "icon" TEXT,
    "color" TEXT,
    "order" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "document_category_pkey" PRIMARY KEY ("id")
);

-- Enhanced Indexes
CREATE INDEX "document_requirement_service_id_idx" ON "document_requirement"("immigration_service_id");
CREATE INDEX "document_requirement_category_id_idx" ON "document_requirement"("category_id");
CREATE INDEX "document_requirement_order_idx" ON "document_requirement"("order");
CREATE INDEX "document_requirement_active_idx" ON "document_requirement"("is_active");
```

#### 4. Application Documents Table
```sql
CREATE TABLE "application_document" (
    "id" TEXT NOT NULL,
    "application_id" TEXT NOT NULL,
    "document_requirement_id" TEXT NOT NULL,
    "file_name" TEXT NOT NULL,
    "file_url" TEXT NOT NULL,
    "file_size" INTEGER NOT NULL,
    "file_type" TEXT NOT NULL,
    "upload_date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" "DocumentStatus" NOT NULL DEFAULT 'PENDING',
    "admin_notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "application_document_pkey" PRIMARY KEY ("id")
);

-- Document Status Enum
CREATE TYPE "DocumentStatus" AS ENUM (
    'PENDING',
    'APPROVED',
    'REJECTED',
    'NEEDS_REPLACEMENT'
);

-- Indexes
CREATE INDEX "application_document_application_id_idx" ON "application_document"("application_id");
CREATE INDEX "application_document_status_idx" ON "application_document"("status");
```

#### 5. Workflow Steps Table
```sql
CREATE TABLE "workflow_step" (
    "id" TEXT NOT NULL,
    "immigration_service_id" TEXT NOT NULL,
    "step_number" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "estimated_duration_days" INTEGER,
    "is_customer_action_required" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "workflow_step_pkey" PRIMARY KEY ("id")
);

-- Indexes
CREATE INDEX "workflow_step_service_id_idx" ON "workflow_step"("immigration_service_id");
CREATE INDEX "workflow_step_number_idx" ON "workflow_step"("step_number");
```

#### 6. Application Timeline Table
```sql
CREATE TABLE "application_timeline" (
    "id" TEXT NOT NULL,
    "application_id" TEXT NOT NULL,
    "event_type" "TimelineEventType" NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "created_by_user_id" TEXT,
    "created_by_admin_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "application_timeline_pkey" PRIMARY KEY ("id")
);

-- Timeline Event Types
CREATE TYPE "TimelineEventType" AS ENUM (
    'APPLICATION_CREATED',
    'PAYMENT_COMPLETED',
    'DOCUMENT_UPLOADED',
    'DOCUMENT_APPROVED',
    'DOCUMENT_REJECTED',
    'STATUS_CHANGED',
    'STEP_COMPLETED',
    'ADMIN_NOTE_ADDED',
    'APPLICATION_SUBMITTED',
    'APPLICATION_COMPLETED'
);

-- Indexes
CREATE INDEX "application_timeline_application_id_idx" ON "application_timeline"("application_id");
CREATE INDEX "application_timeline_event_type_idx" ON "application_timeline"("event_type");
```

### Foreign Key Constraints
```sql
-- Customer Application constraints
ALTER TABLE "customer_application" ADD CONSTRAINT "customer_application_user_id_fkey"
    FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "customer_application" ADD CONSTRAINT "customer_application_immigration_service_id_fkey"
    FOREIGN KEY ("immigration_service_id") REFERENCES "immigration_service"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "customer_application" ADD CONSTRAINT "customer_application_payment_id_fkey"
    FOREIGN KEY ("payment_id") REFERENCES "payment"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Document Requirements constraints
ALTER TABLE "document_requirement" ADD CONSTRAINT "document_requirement_immigration_service_id_fkey"
    FOREIGN KEY ("immigration_service_id") REFERENCES "immigration_service"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Application Documents constraints
ALTER TABLE "application_document" ADD CONSTRAINT "application_document_application_id_fkey"
    FOREIGN KEY ("application_id") REFERENCES "customer_application"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "application_document" ADD CONSTRAINT "application_document_requirement_id_fkey"
    FOREIGN KEY ("document_requirement_id") REFERENCES "document_requirement"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Workflow Steps constraints
ALTER TABLE "workflow_step" ADD CONSTRAINT "workflow_step_immigration_service_id_fkey"
    FOREIGN KEY ("immigration_service_id") REFERENCES "immigration_service"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Application Timeline constraints
ALTER TABLE "application_timeline" ADD CONSTRAINT "application_timeline_application_id_fkey"
    FOREIGN KEY ("application_id") REFERENCES "customer_application"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "application_timeline" ADD CONSTRAINT "application_timeline_created_by_user_id_fkey"
    FOREIGN KEY ("created_by_user_id") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "application_timeline" ADD CONSTRAINT "application_timeline_created_by_admin_id_fkey"
    FOREIGN KEY ("created_by_admin_id") REFERENCES "admin"("id") ON DELETE SET NULL ON UPDATE CASCADE;
```

## Implementation Strategy

### Phase 1: Database Schema and Models (Week 1)

#### 1.1 Create Prisma Schema Extensions
```prisma
// Add to prisma/schema/customer.prisma
model customer_application {
  id                     String                @id @default(cuid())
  application_id         String                @unique
  user_id                String
  immigration_service_id String
  payment_id             String
  status                 ApplicationStatus     @default(PENDING)
  current_step           Int                   @default(1)
  total_steps            Int
  created_at             DateTime              @default(now())
  updated_at             DateTime              @updatedAt
  submitted_at           DateTime?
  completed_at           DateTime?
  notes                  String?

  // Relations
  user                   user                  @relation(fields: [user_id], references: [id], onDelete: Cascade)
  immigration_service    immigration_service   @relation(fields: [immigration_service_id], references: [id], onDelete: Restrict)
  payment                payment               @relation(fields: [payment_id], references: [id], onDelete: Restrict)
  documents              application_document[]
  timeline               application_timeline[]

  @@index([user_id])
  @@index([status])
  @@index([application_id])
}

model document_requirement {
  id                     String                @id @default(cuid())
  immigration_service_id String
  name                   String
  description            String?
  is_required            Boolean               @default(true)
  file_types             String[]
  max_file_size          Int                   @default(25000000)
  order                  Int                   @default(0)
  created_at             DateTime              @default(now())
  updated_at             DateTime              @updatedAt

  // Relations
  immigration_service    immigration_service   @relation(fields: [immigration_service_id], references: [id], onDelete: Cascade)
  application_documents  application_document[]

  @@index([immigration_service_id])
  @@index([order])
}

model application_document {
  id                      String                @id @default(cuid())
  application_id          String
  document_requirement_id String
  file_name               String
  file_url                String
  file_size               Int
  file_type               String
  upload_date             DateTime              @default(now())
  status                  DocumentStatus        @default(PENDING)
  admin_notes             String?
  created_at              DateTime              @default(now())
  updated_at              DateTime              @updatedAt

  // Relations
  application             customer_application  @relation(fields: [application_id], references: [id], onDelete: Cascade)
  document_requirement    document_requirement  @relation(fields: [document_requirement_id], references: [id], onDelete: Restrict)

  @@index([application_id])
  @@index([status])
}

model workflow_step {
  id                          String              @id @default(cuid())
  immigration_service_id      String
  step_number                 Int
  name                        String
  description                 String?
  estimated_duration_days     Int?
  is_customer_action_required Boolean             @default(false)
  created_at                  DateTime            @default(now())
  updated_at                  DateTime            @updatedAt

  // Relations
  immigration_service         immigration_service @relation(fields: [immigration_service_id], references: [id], onDelete: Cascade)

  @@index([immigration_service_id])
  @@index([step_number])
}

model application_timeline {
  id                  String              @id @default(cuid())
  application_id      String
  event_type          TimelineEventType
  title               String
  description         String?
  created_by_user_id  String?
  created_by_admin_id String?
  created_at          DateTime            @default(now())

  // Relations
  application         customer_application @relation(fields: [application_id], references: [id], onDelete: Cascade)
  created_by_user     user?               @relation(fields: [created_by_user_id], references: [id], onDelete: SetNull)
  created_by_admin    admin?              @relation(fields: [created_by_admin_id], references: [id], onDelete: SetNull)

  @@index([application_id])
  @@index([event_type])
}

// Enums
enum ApplicationStatus {
  PENDING
  DOCUMENT_COLLECTION
  UNDER_REVIEW
  ADDITIONAL_INFO_REQUIRED
  PROCESSING
  APPROVED
  REJECTED
  COMPLETED
}

enum DocumentStatus {
  PENDING
  APPROVED
  REJECTED
  NEEDS_REPLACEMENT
}

enum TimelineEventType {
  APPLICATION_CREATED
  PAYMENT_COMPLETED
  DOCUMENT_UPLOADED
  DOCUMENT_APPROVED
  DOCUMENT_REJECTED
  STATUS_CHANGED
  STEP_COMPLETED
  ADMIN_NOTE_ADDED
  APPLICATION_SUBMITTED
  APPLICATION_COMPLETED
}
```

#### 1.2 Create Migration Scripts
```typescript
// scripts/create-customer-dashboard-tables.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createCustomerDashboardTables() {
  console.log('Creating customer dashboard tables...');

  // Create enums first
  await prisma.$executeRaw`
    CREATE TYPE "ApplicationStatus" AS ENUM (
      'PENDING',
      'DOCUMENT_COLLECTION',
      'UNDER_REVIEW',
      'ADDITIONAL_INFO_REQUIRED',
      'PROCESSING',
      'APPROVED',
      'REJECTED',
      'COMPLETED'
    );
  `;

  await prisma.$executeRaw`
    CREATE TYPE "DocumentStatus" AS ENUM (
      'PENDING',
      'APPROVED',
      'REJECTED',
      'NEEDS_REPLACEMENT'
    );
  `;

  await prisma.$executeRaw`
    CREATE TYPE "TimelineEventType" AS ENUM (
      'APPLICATION_CREATED',
      'PAYMENT_COMPLETED',
      'DOCUMENT_UPLOADED',
      'DOCUMENT_APPROVED',
      'DOCUMENT_REJECTED',
      'STATUS_CHANGED',
      'STEP_COMPLETED',
      'ADMIN_NOTE_ADDED',
      'APPLICATION_SUBMITTED',
      'APPLICATION_COMPLETED'
    );
  `;

  console.log('Customer dashboard tables created successfully!');
}

createCustomerDashboardTables()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
```

### Phase 2: Core Services Implementation (Week 2)

#### 2.1 Customer Application Service
```typescript
// src/customer-dashboard/services/customer-application.service.ts
import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from 'src/utils/prisma.service';
import { CreateApplicationDto, UpdateApplicationStatusDto } from '../dto';
import { ApplicationStatus, TimelineEventType } from '@prisma/client';

@Injectable()
export class CustomerApplicationService {
  constructor(private prisma: PrismaService) {}

  async createApplication(userId: string, paymentId: string) {
    // Get payment details to extract service information
    const payment = await this.prisma.payment.findUnique({
      where: { id: paymentId },
      include: { immigration_service: true },
    });

    if (!payment || payment.userId !== userId) {
      throw new NotFoundException('Payment not found or unauthorized');
    }

    if (payment.service_type !== 'immigration') {
      throw new BadRequestException('Payment is not for immigration service');
    }

    // Generate application ID
    const applicationId = await this.generateApplicationId();

    // Get workflow steps for the service
    const workflowSteps = await this.prisma.workflow_step.findMany({
      where: { immigration_service_id: payment.immigration_serviceId },
      orderBy: { step_number: 'asc' },
    });

    const totalSteps = workflowSteps.length || 1;

    // Create application
    const application = await this.prisma.customer_application.create({
      data: {
        application_id: applicationId,
        user_id: userId,
        immigration_service_id: payment.immigration_serviceId,
        payment_id: paymentId,
        status: ApplicationStatus.PENDING,
        current_step: 1,
        total_steps: totalSteps,
      },
      include: {
        immigration_service: true,
        payment: true,
        user: { select: { name: true, email: true } },
      },
    });

    // Create timeline entry
    await this.createTimelineEntry(
      application.id,
      TimelineEventType.APPLICATION_CREATED,
      'Application Created',
      `Application ${applicationId} has been created for ${payment.immigration_service.name}`,
      userId,
    );

    // Create payment completed timeline entry
    await this.createTimelineEntry(
      application.id,
      TimelineEventType.PAYMENT_COMPLETED,
      'Payment Completed',
      `Payment of $${payment.amount / 100} has been processed successfully`,
      userId,
    );

    return application;
  }

  async getApplicationsByUser(userId: string, page = 1, limit = 10) {
    const skip = (page - 1) * limit;

    const [applications, total] = await Promise.all([
      this.prisma.customer_application.findMany({
        where: { user_id: userId },
        include: {
          immigration_service: true,
          payment: true,
          documents: {
            include: { document_requirement: true },
          },
        },
        orderBy: { created_at: 'desc' },
        skip,
        take: limit,
      }),
      this.prisma.customer_application.count({
        where: { user_id: userId },
      }),
    ]);

    return {
      data: applications,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getApplicationById(applicationId: string, userId: string) {
    const application = await this.prisma.customer_application.findFirst({
      where: {
        id: applicationId,
        user_id: userId,
      },
      include: {
        immigration_service: true,
        payment: true,
        documents: {
          include: { document_requirement: true },
          orderBy: { upload_date: 'desc' },
        },
        timeline: {
          orderBy: { created_at: 'desc' },
          include: {
            created_by_user: { select: { name: true } },
            created_by_admin: { select: { name: true } },
          },
        },
      },
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    // Get workflow steps
    const workflowSteps = await this.prisma.workflow_step.findMany({
      where: { immigration_service_id: application.immigration_service_id },
      orderBy: { step_number: 'asc' },
    });

    // Get document requirements
    const documentRequirements = await this.prisma.document_requirement.findMany({
      where: { immigration_service_id: application.immigration_service_id },
      orderBy: { order: 'asc' },
    });

    return {
      ...application,
      workflow_steps: workflowSteps,
      document_requirements: documentRequirements,
    };
  }

  async updateApplicationStatus(
    applicationId: string,
    status: ApplicationStatus,
    adminId?: string,
    notes?: string,
  ) {
    const application = await this.prisma.customer_application.update({
      where: { id: applicationId },
      data: {
        status,
        notes,
        updated_at: new Date(),
        ...(status === ApplicationStatus.COMPLETED && { completed_at: new Date() }),
      },
      include: {
        user: { select: { name: true, email: true } },
        immigration_service: true,
      },
    });

    // Create timeline entry
    await this.createTimelineEntry(
      applicationId,
      TimelineEventType.STATUS_CHANGED,
      'Status Updated',
      `Application status changed to ${status}${notes ? `. Notes: ${notes}` : ''}`,
      undefined,
      adminId,
    );

    return application;
  }

  private async generateApplicationId(): Promise<string> {
    const year = new Date().getFullYear();
    const prefix = `CI-${year}`;

    // Get the latest application for this year
    const latestApplication = await this.prisma.customer_application.findFirst({
      where: {
        application_id: {
          startsWith: prefix,
        },
      },
      orderBy: { created_at: 'desc' },
    });

    let nextNumber = 1;
    if (latestApplication) {
      const lastNumber = parseInt(latestApplication.application_id.split('-')[2]);
      nextNumber = lastNumber + 1;
    }

    return `${prefix}-${nextNumber.toString().padStart(3, '0')}`;
  }

  private async createTimelineEntry(
    applicationId: string,
    eventType: TimelineEventType,
    title: string,
    description: string,
    userId?: string,
    adminId?: string,
  ) {
    return this.prisma.application_timeline.create({
      data: {
        application_id: applicationId,
        event_type: eventType,
        title,
        description,
        created_by_user_id: userId,
        created_by_admin_id: adminId,
      },
    });
  }
}
```

#### 2.2 Document Management Service
```typescript
// src/customer-dashboard/services/document-management.service.ts
import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from 'src/utils/prisma.service';
import { MediaService } from 'src/media/media.service';
import { UploadDocumentDto, UpdateDocumentStatusDto } from '../dto';
import { DocumentStatus, TimelineEventType } from '@prisma/client';

@Injectable()
export class DocumentManagementService {
  constructor(
    private prisma: PrismaService,
    private mediaService: MediaService,
  ) {}

  async uploadDocument(
    applicationId: string,
    userId: string,
    documentRequirementId: string,
    file: Express.Multer.File,
  ) {
    // Verify application belongs to user
    const application = await this.prisma.customer_application.findFirst({
      where: {
        id: applicationId,
        user_id: userId,
      },
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    // Get document requirement
    const requirement = await this.prisma.document_requirement.findUnique({
      where: { id: documentRequirementId },
    });

    if (!requirement) {
      throw new NotFoundException('Document requirement not found');
    }

    // Validate file type
    const fileExtension = file.originalname.split('.').pop()?.toLowerCase();
    if (!requirement.file_types.includes(fileExtension)) {
      throw new BadRequestException(
        `File type ${fileExtension} not allowed. Allowed types: ${requirement.file_types.join(', ')}`,
      );
    }

    // Validate file size
    if (file.size > requirement.max_file_size) {
      throw new BadRequestException(
        `File size exceeds maximum allowed size of ${requirement.max_file_size / 1000000}MB`,
      );
    }

    // Upload file to storage
    const uploadResult = await this.mediaService.uploadFile(file, 'application-documents');

    // Save document record
    const document = await this.prisma.application_document.create({
      data: {
        application_id: applicationId,
        document_requirement_id: documentRequirementId,
        file_name: file.originalname,
        file_url: uploadResult.url,
        file_size: file.size,
        file_type: fileExtension,
        status: DocumentStatus.PENDING,
      },
      include: {
        document_requirement: true,
        application: {
          include: {
            user: { select: { name: true, email: true } },
          },
        },
      },
    });

    // Create timeline entry
    await this.createTimelineEntry(
      applicationId,
      TimelineEventType.DOCUMENT_UPLOADED,
      'Document Uploaded',
      `${requirement.name} has been uploaded and is pending review`,
      userId,
    );

    return document;
  }

  async getDocumentsByApplication(applicationId: string, userId: string) {
    // Verify application belongs to user
    const application = await this.prisma.customer_application.findFirst({
      where: {
        id: applicationId,
        user_id: userId,
      },
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    return this.prisma.application_document.findMany({
      where: { application_id: applicationId },
      include: { document_requirement: true },
      orderBy: { upload_date: 'desc' },
    });
  }

  async updateDocumentStatus(
    documentId: string,
    status: DocumentStatus,
    adminId: string,
    adminNotes?: string,
  ) {
    const document = await this.prisma.application_document.update({
      where: { id: documentId },
      data: {
        status,
        admin_notes: adminNotes,
        updated_at: new Date(),
      },
      include: {
        document_requirement: true,
        application: {
          include: {
            user: { select: { name: true, email: true } },
          },
        },
      },
    });

    // Create timeline entry
    const eventType = status === DocumentStatus.APPROVED
      ? TimelineEventType.DOCUMENT_APPROVED
      : TimelineEventType.DOCUMENT_REJECTED;

    await this.createTimelineEntry(
      document.application_id,
      eventType,
      `Document ${status}`,
      `${document.document_requirement.name} has been ${status.toLowerCase()}${adminNotes ? `. Notes: ${adminNotes}` : ''}`,
      undefined,
      adminId,
    );

    return document;
  }

  private async createTimelineEntry(
    applicationId: string,
    eventType: TimelineEventType,
    title: string,
    description: string,
    userId?: string,
    adminId?: string,
  ) {
    return this.prisma.application_timeline.create({
      data: {
        application_id: applicationId,
        event_type: eventType,
        title,
        description,
        created_by_user_id: userId,
        created_by_admin_id: adminId,
      },
    });
  }
}
```

### Phase 3: API Controllers Implementation (Week 3)

#### 3.1 Customer Dashboard Controller
```typescript
// src/customer-dashboard/controllers/customer-dashboard.controller.ts
import {
  Controller,
  Get,
  Post,
  Patch,
  Param,
  Query,
  Body,
  UseGuards,
  UploadedFile,
  UseInterceptors,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { JwtGuard } from 'src/guards/jwt.guard';
import { GetUser } from 'src/decorator/user.decorator';
import { IJWTPayload } from 'src/types/auth';
import { CustomerApplicationService } from '../services/customer-application.service';
import { DocumentManagementService } from '../services/document-management.service';
import { CreateApplicationDto, UploadDocumentDto } from '../dto';

@ApiTags('customer-dashboard')
@Controller('customer-dashboard')
@UseGuards(JwtGuard)
@ApiBearerAuth()
export class CustomerDashboardController {
  constructor(
    private applicationService: CustomerApplicationService,
    private documentService: DocumentManagementService,
  ) {}

  @Get('applications')
  @ApiOperation({
    summary: 'Get user applications',
    description: 'Get paginated list of applications for the authenticated user',
  })
  async getApplications(
    @GetUser() user: IJWTPayload,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return await this.applicationService.getApplicationsByUser(user.id, page, limit);
  }

  @Get('applications/:id')
  @ApiOperation({
    summary: 'Get application details',
    description: 'Get detailed information about a specific application',
  })
  async getApplication(
    @GetUser() user: IJWTPayload,
    @Param('id') applicationId: string,
  ) {
    return await this.applicationService.getApplicationById(applicationId, user.id);
  }

  @Post('applications')
  @ApiOperation({
    summary: 'Create application from payment',
    description: 'Create a new application based on a completed payment',
  })
  async createApplication(
    @GetUser() user: IJWTPayload,
    @Body() dto: CreateApplicationDto,
  ) {
    return await this.applicationService.createApplication(user.id, dto.paymentId);
  }

  @Post('applications/:id/documents')
  @ApiOperation({
    summary: 'Upload document',
    description: 'Upload a document for a specific application',
  })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        documentRequirementId: {
          type: 'string',
        },
      },
    },
  })
  async uploadDocument(
    @GetUser() user: IJWTPayload,
    @Param('id') applicationId: string,
    @Body('documentRequirementId') documentRequirementId: string,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return await this.documentService.uploadDocument(
      applicationId,
      user.id,
      documentRequirementId,
      file,
    );
  }

  @Get('applications/:id/documents')
  @ApiOperation({
    summary: 'Get application documents',
    description: 'Get all documents for a specific application',
  })
  async getApplicationDocuments(
    @GetUser() user: IJWTPayload,
    @Param('id') applicationId: string,
  ) {
    return await this.documentService.getDocumentsByApplication(applicationId, user.id);
  }

  @Get('dashboard-stats')
  @ApiOperation({
    summary: 'Get dashboard statistics',
    description: 'Get overview statistics for the customer dashboard',
  })
  async getDashboardStats(@GetUser() user: IJWTPayload) {
    const applications = await this.applicationService.getApplicationsByUser(user.id, 1, 100);

    const stats = {
      total_applications: applications.pagination.total,
      pending_applications: applications.data.filter(app => app.status === 'PENDING').length,
      in_progress_applications: applications.data.filter(app =>
        ['DOCUMENT_COLLECTION', 'UNDER_REVIEW', 'PROCESSING'].includes(app.status)
      ).length,
      completed_applications: applications.data.filter(app => app.status === 'COMPLETED').length,
      recent_applications: applications.data.slice(0, 5),
    };

    return stats;
  }
}
```

#### 3.2 Admin Application Management Controller
```typescript
// src/customer-dashboard/controllers/admin-application.controller.ts
import {
  Controller,
  Get,
  Patch,
  Param,
  Query,
  Body,
  UseGuards,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation } from '@nestjs/swagger';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';
import { GetAdmin } from 'src/decorator/admin.decorator';
import { IJWTPayload } from 'src/types/auth';
import { CustomerApplicationService } from '../services/customer-application.service';
import { DocumentManagementService } from '../services/document-management.service';
import { UpdateApplicationStatusDto, UpdateDocumentStatusDto } from '../dto';

@ApiTags('admin-applications')
@Controller('admin/applications')
@UseGuards(JwtAdmin)
@ApiBearerAuth()
export class AdminApplicationController {
  constructor(
    private applicationService: CustomerApplicationService,
    private documentService: DocumentManagementService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get all applications (Admin only)',
    description: 'Get paginated list of all customer applications',
  })
  async getAllApplications(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('status') status?: string,
  ) {
    // Implementation for admin to view all applications
    return { message: 'Admin applications endpoint - to be implemented' };
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get application details (Admin only)',
    description: 'Get detailed information about any application',
  })
  async getApplication(@Param('id') applicationId: string) {
    // Implementation for admin to view any application
    return { message: 'Admin application details endpoint - to be implemented' };
  }

  @Patch(':id/status')
  @ApiOperation({
    summary: 'Update application status (Admin only)',
    description: 'Update the status of a customer application',
  })
  async updateApplicationStatus(
    @GetAdmin() admin: IJWTPayload,
    @Param('id') applicationId: string,
    @Body() dto: UpdateApplicationStatusDto,
  ) {
    return await this.applicationService.updateApplicationStatus(
      applicationId,
      dto.status,
      admin.id,
      dto.notes,
    );
  }

  @Patch('documents/:documentId/status')
  @ApiOperation({
    summary: 'Update document status (Admin only)',
    description: 'Approve or reject a customer document',
  })
  async updateDocumentStatus(
    @GetAdmin() admin: IJWTPayload,
    @Param('documentId') documentId: string,
    @Body() dto: UpdateDocumentStatusDto,
  ) {
    return await this.documentService.updateDocumentStatus(
      documentId,
      dto.status,
      admin.id,
      dto.adminNotes,
    );
  }
}
```

#### 3.3 DTOs (Data Transfer Objects)
```typescript
// src/customer-dashboard/dto/create-application.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class CreateApplicationDto {
  @ApiProperty({ description: 'Payment ID for the immigration service' })
  @IsString()
  @IsNotEmpty()
  paymentId: string;
}

// src/customer-dashboard/dto/update-application-status.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { ApplicationStatus } from '@prisma/client';

export class UpdateApplicationStatusDto {
  @ApiProperty({ enum: ApplicationStatus, description: 'New application status' })
  @IsEnum(ApplicationStatus)
  status: ApplicationStatus;

  @ApiProperty({ description: 'Optional notes for status change', required: false })
  @IsOptional()
  @IsString()
  notes?: string;
}

// src/customer-dashboard/dto/update-document-status.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { DocumentStatus } from '@prisma/client';

export class UpdateDocumentStatusDto {
  @ApiProperty({ enum: DocumentStatus, description: 'New document status' })
  @IsEnum(DocumentStatus)
  status: DocumentStatus;

  @ApiProperty({ description: 'Admin notes for document review', required: false })
  @IsOptional()
  @IsString()
  adminNotes?: string;
}

// src/customer-dashboard/dto/upload-document.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class UploadDocumentDto {
  @ApiProperty({ description: 'Document requirement ID' })
  @IsString()
  @IsNotEmpty()
  documentRequirementId: string;
}

// src/customer-dashboard/dto/index.ts
export * from './create-application.dto';
export * from './update-application-status.dto';
export * from './update-document-status.dto';
export * from './upload-document.dto';
```

### Phase 4: Integration with Payment System (Week 4)

#### 4.1 Update Payment Service to Create Applications
```typescript
// src/payment/payment.service.ts - Add this method
async createApplicationAfterPayment(paymentId: string) {
  const payment = await this.prisma.payment.findUnique({
    where: { id: paymentId },
    include: { user: true, immigration_service: true },
  });

  if (!payment || payment.service_type !== 'immigration') {
    return null;
  }

  // Check if application already exists
  const existingApplication = await this.prisma.customer_application.findFirst({
    where: { payment_id: paymentId },
  });

  if (existingApplication) {
    return existingApplication;
  }

  // Create application using the customer application service
  const applicationService = new CustomerApplicationService(this.prisma);
  return await applicationService.createApplication(payment.userId, paymentId);
}
```

#### 4.2 Update Webhook Handler
```typescript
// src/payment/payment.service.ts - Update webhook method
async webhook(req: any) {
  // ... existing webhook logic ...

  switch (event.type) {
    case 'checkout.session.completed':
      const session = event.data.object;

      // ... existing payment creation logic ...

      // Create application for immigration services
      if (session.metadata.service_type === 'immigration') {
        await this.createApplicationAfterPayment(createdPayment.id);
      }
      break;
  }
}
```

### Phase 5: Email Notification System (Week 5)

#### 5.1 Customer Email Templates
```typescript
// src/template/application-created.tsx
import { Html, Head, Body, Container, Text, Button, Hr } from '@react-email/components';

interface ApplicationCreatedEmailProps {
  customerName: string;
  applicationId: string;
  serviceName: string;
  dashboardUrl: string;
}

export default function ApplicationCreatedEmail({
  customerName,
  applicationId,
  serviceName,
  dashboardUrl,
}: ApplicationCreatedEmailProps) {
  return (
    <Html>
      <Head />
      <Body style={{ fontFamily: 'Arial, sans-serif' }}>
        <Container>
          <Text>Dear {customerName},</Text>
          <Text>
            Your immigration application has been successfully created!
          </Text>
          <Text>
            <strong>Application ID:</strong> {applicationId}<br />
            <strong>Service:</strong> {serviceName}
          </Text>
          <Text>
            You can track your application progress and upload required documents through your customer dashboard.
          </Text>
          <Button href={dashboardUrl} style={{ background: '#007bff', color: 'white', padding: '10px 20px' }}>
            View Dashboard
          </Button>
          <Hr />
          <Text style={{ fontSize: '12px', color: '#666' }}>
            If you have any questions, please contact our support team.
          </Text>
        </Container>
      </Body>
    </Html>
  );
}

// src/template/document-status-update.tsx
import { Html, Head, Body, Container, Text, Button } from '@react-email/components';

interface DocumentStatusUpdateEmailProps {
  customerName: string;
  applicationId: string;
  documentName: string;
  status: string;
  adminNotes?: string;
  dashboardUrl: string;
}

export default function DocumentStatusUpdateEmail({
  customerName,
  applicationId,
  documentName,
  status,
  adminNotes,
  dashboardUrl,
}: DocumentStatusUpdateEmailProps) {
  return (
    <Html>
      <Head />
      <Body style={{ fontFamily: 'Arial, sans-serif' }}>
        <Container>
          <Text>Dear {customerName},</Text>
          <Text>
            There has been an update to your document review for application {applicationId}.
          </Text>
          <Text>
            <strong>Document:</strong> {documentName}<br />
            <strong>Status:</strong> {status}
          </Text>
          {adminNotes && (
            <Text>
              <strong>Notes:</strong> {adminNotes}
            </Text>
          )}
          <Button href={dashboardUrl} style={{ background: '#007bff', color: 'white', padding: '10px 20px' }}>
            View Application
          </Button>
        </Container>
      </Body>
    </Html>
  );
}
```

#### 5.2 Email Notification Service
```typescript
// src/customer-dashboard/services/notification.service.ts
import { Injectable } from '@nestjs/common';
import { MailerService } from 'src/mailer/mailer.service';
import { render } from '@react-email/render';
import ApplicationCreatedEmail from 'src/template/application-created';
import DocumentStatusUpdateEmail from 'src/template/document-status-update';

@Injectable()
export class NotificationService {
  constructor(private mailerService: MailerService) {}

  async sendApplicationCreatedEmail(
    customerEmail: string,
    customerName: string,
    applicationId: string,
    serviceName: string,
  ) {
    const dashboardUrl = `${process.env.CLIENT_URL}/dashboard/applications/${applicationId}`;

    const emailHtml = render(
      ApplicationCreatedEmail({
        customerName,
        applicationId,
        serviceName,
        dashboardUrl,
      }),
    );

    await this.mailerService.sendMail({
      to: customerEmail,
      subject: `Application Created - ${applicationId}`,
      html: emailHtml,
    });
  }

  async sendDocumentStatusUpdateEmail(
    customerEmail: string,
    customerName: string,
    applicationId: string,
    documentName: string,
    status: string,
    adminNotes?: string,
  ) {
    const dashboardUrl = `${process.env.CLIENT_URL}/dashboard/applications/${applicationId}`;

    const emailHtml = render(
      DocumentStatusUpdateEmail({
        customerName,
        applicationId,
        documentName,
        status,
        adminNotes,
        dashboardUrl,
      }),
    );

    await this.mailerService.sendMail({
      to: customerEmail,
      subject: `Document ${status} - ${applicationId}`,
      html: emailHtml,
    });
  }

  async sendApplicationStatusUpdateEmail(
    customerEmail: string,
    customerName: string,
    applicationId: string,
    newStatus: string,
    notes?: string,
  ) {
    // Implementation for application status update emails
    await this.mailerService.sendMail({
      to: customerEmail,
      subject: `Application Status Update - ${applicationId}`,
      html: `
        <h2>Application Status Update</h2>
        <p>Dear ${customerName},</p>
        <p>Your application ${applicationId} status has been updated to: <strong>${newStatus}</strong></p>
        ${notes ? `<p>Notes: ${notes}</p>` : ''}
        <p>Please check your dashboard for more details.</p>
      `,
    });
  }
}
```

### Phase 6: Module Configuration and Integration

#### 6.1 Customer Dashboard Module
```typescript
// src/customer-dashboard/customer-dashboard.module.ts
import { Module } from '@nestjs/common';
import { CustomerDashboardController } from './controllers/customer-dashboard.controller';
import { AdminApplicationController } from './controllers/admin-application.controller';
import { CustomerApplicationService } from './services/customer-application.service';
import { DocumentManagementService } from './services/document-management.service';
import { NotificationService } from './services/notification.service';
import { PrismaService } from 'src/utils/prisma.service';
import { MediaService } from 'src/media/media.service';
import { MailerService } from 'src/mailer/mailer.service';
import { SupabaseService } from 'src/utils/supabase.service';

@Module({
  controllers: [CustomerDashboardController, AdminApplicationController],
  providers: [
    CustomerApplicationService,
    DocumentManagementService,
    NotificationService,
    PrismaService,
    MediaService,
    MailerService,
    SupabaseService,
  ],
  exports: [CustomerApplicationService, DocumentManagementService],
})
export class CustomerDashboardModule {}
```

#### 6.2 Update App Module
```typescript
// src/app.module.ts - Add CustomerDashboardModule to imports
import { CustomerDashboardModule } from './customer-dashboard/customer-dashboard.module';

@Module({
  imports: [
    // ... existing imports
    CustomerDashboardModule,
  ],
  // ... rest of module configuration
})
export class AppModule {}
```

## API Endpoints Summary

### Customer Dashboard Endpoints (User Authentication Required)

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/customer-dashboard/applications` | Get user's applications (paginated) |
| GET | `/customer-dashboard/applications/:id` | Get specific application details |
| POST | `/customer-dashboard/applications` | Create application from payment |
| POST | `/customer-dashboard/applications/:id/documents` | Upload document for application |
| GET | `/customer-dashboard/applications/:id/documents` | Get application documents |
| GET | `/customer-dashboard/dashboard-stats` | Get dashboard statistics |

### Admin Application Management Endpoints (Admin Authentication Required)

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/admin/applications` | Get all applications (admin view) |
| GET | `/admin/applications/:id` | Get application details (admin view) |
| PATCH | `/admin/applications/:id/status` | Update application status |
| PATCH | `/admin/applications/documents/:documentId/status` | Update document status |

## Customer Flow Implementation

### 1. Customer Selects Immigration Plan
- **Existing Feature**: Immigration service selection through `/immigration-service` endpoint
- **Status**: ✅ Already implemented

### 2. Customer Makes Payment
- **Existing Feature**: Payment processing through Stripe integration
- **Status**: ✅ Already implemented
- **Enhancement**: Auto-create application after successful payment

### 3. Customer Login to Dashboard
- **Implementation**:
  - Use existing JWT authentication system
  - New customer dashboard endpoints
  - Dashboard statistics and overview
- **Status**: 🔄 To be implemented

### 4. Customer Application ID for Purchase Service
- **Implementation**:
  - Auto-generate human-readable application IDs (CI-2024-001)
  - Link applications to payments
  - Display application ID in dashboard
- **Status**: 🔄 To be implemented

### 5. Customer Uploads Documents Required for Service
- **Implementation**:
  - Document requirements defined by admin per service
  - File upload with validation (type, size)
  - Document status tracking (pending, approved, rejected)
- **Status**: 🔄 To be implemented

### 6. Customer Flow Workflow Defined by Admin
- **Implementation**:
  - Workflow steps configuration per immigration service
  - Progress tracking through workflow steps
  - Admin-defined customer action requirements
- **Status**: 🔄 To be implemented

### 7. Customer Can View Application Status
- **Implementation**:
  - Real-time status updates
  - Timeline view of application progress
  - Status change notifications
- **Status**: 🔄 To be implemented

### 8. Customer Gets Email Notifications
- **Implementation**:
  - Application created notifications
  - Document status update emails
  - Application status change alerts
  - Missing document reminders
- **Status**: 🔄 To be implemented

## Implementation Timeline

### Week 1: Database Schema and Models
- [ ] Create Prisma schema extensions
- [ ] Generate and run database migrations
- [ ] Update existing models with new relations
- [ ] Create seed data for testing

### Week 2: Core Services Implementation
- [ ] Implement CustomerApplicationService
- [ ] Implement DocumentManagementService
- [ ] Create application ID generation logic
- [ ] Implement timeline tracking

### Week 3: API Controllers and DTOs
- [ ] Create CustomerDashboardController
- [ ] Create AdminApplicationController
- [ ] Implement all DTOs with validation
- [ ] Add Swagger documentation

### Week 4: Payment System Integration
- [ ] Update payment webhook to create applications
- [ ] Integrate with existing payment flow
- [ ] Test end-to-end payment to application flow
- [ ] Update payment service methods

### Week 5: Email Notification System
- [ ] Create email templates
- [ ] Implement NotificationService
- [ ] Integrate notifications with application events
- [ ] Test email delivery

### Week 6: Admin Features and Workflow Management
- [ ] Implement admin document requirement management
- [ ] Create workflow step configuration
- [ ] Add admin application management features
- [ ] Implement bulk operations for admins

### Week 7: Testing and Documentation
- [ ] Write comprehensive unit tests
- [ ] Create integration tests
- [ ] Update API documentation
- [ ] Create user guides and admin documentation

### Week 8: Deployment and Monitoring
- [ ] Deploy to staging environment
- [ ] Performance testing and optimization
- [ ] Set up monitoring and alerts
- [ ] Production deployment

## Security Considerations

### Authentication and Authorization
- Use existing JWT authentication system
- Ensure users can only access their own applications
- Admin-only endpoints properly protected
- File upload security validation

### Data Protection
- Secure file storage with proper access controls
- Encrypt sensitive document data
- GDPR compliance for customer data
- Audit trail for all admin actions

### File Upload Security
- Validate file types and sizes
- Scan uploaded files for malware
- Secure file storage with access controls
- Generate secure file URLs with expiration

## Performance Considerations

### Database Optimization
- Proper indexing on frequently queried fields
- Pagination for large datasets
- Efficient queries with proper joins
- Database connection pooling

### File Storage
- Use CDN for file delivery
- Implement file compression
- Lazy loading for document lists
- Caching for frequently accessed files

### API Performance
- Response caching where appropriate
- Rate limiting for file uploads
- Async processing for heavy operations
- Monitoring and alerting for performance issues

## Monitoring and Analytics

### Application Metrics
- Application creation rates
- Document upload success rates
- Processing time per application
- User engagement metrics

### System Health
- API response times
- Database query performance
- File upload success rates
- Email delivery rates

### Business Intelligence
- Application status distribution
- Most common document issues
- Processing bottlenecks
- Customer satisfaction metrics

## Database Structure Analysis & Improvements

### 🔍 **Current vs Improved Structure Comparison**

#### **Key Improvements Made:**

### 1. **Enhanced Application Management**
- **Added Priority System**: Applications can be prioritized (LOW, NORMAL, HIGH, URGENT)
- **Agent Assignment**: Applications can be assigned to specific agents for better case management
- **Metadata Field**: JSONB field for flexible additional data without schema changes
- **Better Date Tracking**: Separate estimated vs actual completion dates
- **Enhanced Status**: Added DRAFT, CANCELLED, ON_HOLD statuses

### 2. **Flexible Workflow System**
- **Template-Based**: Workflows are now template-based and reusable across services
- **Version Control**: Workflow templates have versioning for better change management
- **Step Dependencies**: Steps can depend on other steps completing first
- **Auto-Completion**: Steps can auto-complete based on defined conditions
- **Step Types**: Categorized step types for better workflow management

### 3. **Advanced Document Management**
- **Document Categories**: Group related documents for better organization
- **Multiple Files**: Support for multiple files per requirement
- **Validation Rules**: Custom validation rules stored as JSONB
- **Document Expiry**: Documents can have expiration dates
- **Better Instructions**: Detailed instructions for customers
- **File Size Ranges**: Both minimum and maximum file size validation

### 4. **Performance Optimizations**
- **Better Indexing**: More comprehensive indexing strategy
- **GIN Indexes**: For JSONB fields to enable fast queries
- **Composite Indexes**: For common query patterns
- **Partitioning Ready**: Structure supports future table partitioning

### 5. **Scalability Improvements**
- **Reusable Components**: Document requirements and workflows can be reused
- **Flexible Schema**: JSONB fields allow evolution without migrations
- **Better Normalization**: Reduced data duplication
- **Audit Trail Ready**: Structure supports comprehensive audit logging

## **Alternative Data Structure Considerations**

### **Option A: Event Sourcing Pattern**
```sql
-- Store all changes as events
CREATE TABLE "application_events" (
    "id" TEXT NOT NULL,
    "application_id" TEXT NOT NULL,
    "event_type" TEXT NOT NULL,
    "event_data" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT NOT NULL
);
```
**Pros**: Complete audit trail, time-travel queries, easy rollbacks
**Cons**: More complex queries, larger storage requirements

### **Option B: Document-Based (MongoDB-style)**
```sql
-- Single table with all data as JSONB
CREATE TABLE "applications" (
    "id" TEXT NOT NULL,
    "data" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL
);
```
**Pros**: Maximum flexibility, easy schema evolution
**Cons**: Loss of referential integrity, complex queries, no type safety

### **Option C: Microservices-Oriented**
```sql
-- Separate databases for different domains
-- Application Service DB
-- Document Service DB
-- Workflow Service DB
-- Notification Service DB
```
**Pros**: Service independence, technology diversity, scalability
**Cons**: Distributed transactions, data consistency challenges, complexity

## **Recommended Approach: Hybrid Enhanced Structure**

I recommend the **Enhanced Structure** I've proposed because it provides:

### ✅ **Best Balance of:**
- **Flexibility** without sacrificing data integrity
- **Performance** with proper indexing and normalization
- **Scalability** with reusable components and JSONB flexibility
- **Maintainability** with clear relationships and constraints
- **Evolution** capability through versioning and metadata fields

### 🎯 **Specific Benefits:**

1. **Workflow Flexibility**: Templates can be created once and reused across multiple services
2. **Document Reusability**: Common documents (passport, ID) can be defined once
3. **Performance**: Proper indexing for all common query patterns
4. **Reporting**: Structure optimized for business intelligence queries
5. **Audit Trail**: Complete timeline tracking with enhanced metadata
6. **Future-Proof**: JSONB fields allow adding new features without schema changes

### 📊 **Query Performance Examples:**

```sql
-- Fast queries with proper indexing
SELECT * FROM customer_application
WHERE status = 'PENDING' AND priority = 'HIGH'
AND assigned_agent_id = 'agent123';

-- Efficient document searches
SELECT * FROM application_document ad
JOIN document_requirement dr ON ad.document_requirement_id = dr.id
JOIN document_category dc ON dr.category_id = dc.id
WHERE ad.application_id = 'app123' AND dc.name = 'Identity Documents';

-- Workflow progress tracking
SELECT wst.name, wst.step_number,
       CASE WHEN ca.current_step >= wst.step_number THEN 'COMPLETED' ELSE 'PENDING' END as status
FROM workflow_step_template wst
JOIN customer_application ca ON ca.workflow_template_id = wst.workflow_template_id
WHERE ca.id = 'app123'
ORDER BY wst.step_number;
```

This enhanced structure provides the best foundation for building a robust, scalable customer dashboard while maintaining the flexibility to evolve with changing business requirements.

This implementation plan provides a comprehensive roadmap for building the Customer Dashboard feature while leveraging existing infrastructure and following the established patterns in the codebase.