/**
 * Payment Controller
 *
 * Handles all payment-related HTTP endpoints for the Career Ireland platform.
 * This controller provides REST API endpoints for processing payments across
 * multiple service types including mentor services, packages, immigration
 * services, and training programs.
 *
 * Features:
 * - Authenticated user payments (requires JWT token)
 * - Guest user payments (no authentication required)
 * - Stripe webhook handling for payment confirmations
 * - Support for multiple service types
 *
 * Security:
 * - JWT authentication for user endpoints
 * - Input validation using DTOs
 * - Stripe webhook signature verification
 *
 * REVIEW NOTES:
 * - Consider implementing rate limiting for payment endpoints
 * - Add request/response logging for audit trails
 * - Consider adding payment amount validation middleware
 * - Webhook endpoint should have IP whitelisting for Stripe
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2024-12-27
 */

// Core NestJS imports for controller functionality
import {
  Body,
  Controller,
  Post,
  RawBodyRequest,
  Req,
  UseGuards,
} from '@nestjs/common';
// Swagger documentation decorators
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
// Payment business logic service
import { PaymentService } from './payment.service';
// JWT authentication guard
import { JwtGuard } from 'src/guards/jwt.guard';
// Data Transfer Objects for request validation
import {
  UserImmigrationServiceDto,
  UserMentorServiceDto,
  UserPackageServiceDto,
  UserTrainingServiceDto,
} from './dto/payment.dto';
// Custom decorator for extracting user from JWT
import { GetUser } from 'src/decorator/user.decorator';
// Fastify request type for webhook handling
import { FastifyRequest } from 'fastify';

// TODO: Add missing import for IJWTPayload interface
// This should be imported from a types/interfaces file
// Example: import { IJWTPayload } from 'src/types/auth.types';

@ApiTags('payment')
@Controller('payment')
export class PaymentController {
  /**
   * Payment Controller Constructor
   *
   * Initializes the payment controller with the payment service dependency.
   * Uses NestJS dependency injection for loose coupling and testability.
   *
   * @param payment - Payment service instance for handling payment operations
   *
   * REVIEW NOTE: Consider renaming parameter to 'paymentService' for clarity
   */
  constructor(private payment: PaymentService) {}

  /**
   * Process Mentor Service Payment for Authenticated Users
   *
   * Creates a Stripe payment intent for authenticated users purchasing mentor services.
   * This endpoint requires JWT authentication and validates all input data.
   *
   * @param user - Authenticated user object extracted from JWT token
   * @param dto - Validated mentor service payment data (service ID, amount, etc.)
   * @returns Promise<PaymentIntent> - Stripe payment intent with client secret for frontend
   *
   * SECURITY CONSIDERATIONS:
   * - JWT guard ensures user authentication
   * - DTO validation prevents malformed requests
   * - Consider adding rate limiting (e.g., 10 requests per minute per user)
   * - Add payment amount validation against actual service pricing
   *
   * IMPROVEMENTS NEEDED:
   * - Replace 'IJWTPayload' import (seems to be missing)
   * - Add error handling documentation
   * - Consider adding request logging for audit trails
   */
  @UseGuards(JwtGuard) // Enforces JWT authentication
  @ApiBearerAuth() // Swagger documentation for auth requirement
  @Post('/mentor-service')
  @ApiOperation({
    summary: 'Process mentor service payment (Authenticated users only)',
    description:
      'Creates a payment intent for authenticated users to purchase mentor services. Requires valid JWT token.',
  })
  async create(
    @GetUser() user: IJWTPayload, // TODO: Verify IJWTPayload import exists
    @Body() dto: UserMentorServiceDto,
  ) {
    // Delegate business logic to service layer - good separation of concerns
    return await this.payment.mentor_service(user, dto);
  }
  /**
   * Process Mentor Service Payment for Guest Users
   *
   * Allows unauthenticated users to purchase mentor services without registration.
   * Guest payments typically require additional contact information for service delivery.
   *
   * @param dto - Mentor service payment data including contact details for guest users
   * @returns Promise<PaymentIntent> - Stripe payment intent for guest checkout
   *
   * SECURITY CONSIDERATIONS:
   * - No authentication required - higher risk of abuse
   * - CRITICAL: Implement rate limiting (e.g., 5 requests per IP per hour)
   * - Add CAPTCHA verification to prevent bot abuse
   * - Validate email format and consider email verification
   *
   * BUSINESS LOGIC:
   * - Guest payments may have different pricing or restrictions
   * - Ensure contact information is captured for service delivery
   */
  @Post('/guest-service')
  @ApiOperation({
    summary: 'Process mentor service payment (Guest users - no authentication)',
    description:
      'Allows unauthenticated users to purchase mentor services with contact information.',
  })
  async guestService(@Body() dto: UserMentorServiceDto) {
    // SECURITY CONCERN: No rate limiting or abuse prevention visible
    return await this.payment.guest_service(dto);
  }

  /**
   * Process Package Payment for Authenticated Users
   *
   * Handles payment processing for service packages (bundles of multiple services).
   * Packages typically offer discounted pricing compared to individual services.
   *
   * @param user - Authenticated user object from JWT token
   * @param dto - Package payment data including package ID and pricing
   * @returns Promise<PaymentIntent> - Stripe payment intent for package purchase
   *
   * BUSINESS CONSIDERATIONS:
   * - Validate package availability and pricing
   * - Check user eligibility for package discounts
   * - Consider package expiration dates and terms
   */
  @UseGuards(JwtGuard) // Requires authentication
  @ApiBearerAuth()
  @Post('/package')
  @ApiOperation({
    summary: 'Process package payment (Authenticated users only)',
    description:
      'Creates payment intent for service packages. Requires valid JWT token for user identification.',
  })
  async user_package(
    @GetUser() user: IJWTPayload,
    @Body() dto: UserPackageServiceDto,
  ) {
    return await this.payment.user_package(user, dto);
  }

  /**
   * Process Package Payment for Guest Users
   *
   * Enables unauthenticated users to purchase service packages.
   * Guest package purchases require comprehensive contact information.
   *
   * @param dto - Package payment data with guest contact information
   * @returns Promise<PaymentIntent> - Stripe payment intent for guest package purchase
   *
   * SECURITY RISK: Same concerns as guest service endpoint
   * - Implement rate limiting and abuse prevention
   * - Add CAPTCHA or similar verification
   */
  @Post('/guest-package')
  @ApiOperation({
    summary: 'Process package payment (Guest users - no authentication)',
    description: 'Allows unauthenticated users to purchase service packages.',
  })
  async guestPackage(@Body() dto: UserPackageServiceDto) {
    return await this.payment.guest_package(dto);
  }

  /**
   * Process Immigration Service Payment for Authenticated Users
   *
   * Handles payment processing for immigration-related services (visa applications,
   * document preparation, consultation services, etc.). These services often involve
   * sensitive personal information and legal compliance requirements.
   *
   * @param user - Authenticated user object from JWT token
   * @param dto - Immigration service payment data with service details
   * @returns Promise<PaymentIntent> - Stripe payment intent for immigration service
   *
   * COMPLIANCE CONSIDERATIONS:
   * - Immigration services may have regulatory requirements
   * - Ensure GDPR compliance for EU citizens
   * - Consider data retention policies for immigration documents
   * - May require additional identity verification
   *
   * SECURITY REQUIREMENTS:
   * - Enhanced logging for audit trails (immigration law compliance)
   * - Secure handling of sensitive personal information
   * - Consider additional authentication for high-value services
   */
  @UseGuards(JwtGuard) // Authentication required for immigration services
  @ApiBearerAuth()
  @Post('/immigration-service')
  @ApiOperation({
    summary: 'Process immigration service payment (Authenticated users only)',
    description:
      'Creates payment intent for immigration services. Requires authentication due to sensitive nature of services.',
  })
  async user_immigration(
    @GetUser() user: IJWTPayload,
    @Body() dto: UserImmigrationServiceDto,
  ) {
    return await this.payment.user_immigration(user, dto);
  }

  /**
   * Process Immigration Service Payment for Guest Users
   *
   * Allows unauthenticated users to purchase immigration services.
   * Given the sensitive nature of immigration services, this endpoint
   * should have enhanced security measures.
   *
   * @param dto - Immigration service payment data with comprehensive contact info
   * @returns Promise<PaymentIntent> - Stripe payment intent for guest immigration service
   *
   * HIGH SECURITY RISK:
   * - Immigration services involve sensitive personal data
   * - CRITICAL: Implement strict rate limiting (2-3 requests per IP per day)
   * - REQUIRED: Add identity verification (phone/email verification)
   * - Consider requiring registration for immigration services
   * - Enhanced fraud detection needed
   *
   * COMPLIANCE RISK:
   * - Guest users harder to verify for regulatory compliance
   * - Consider legal implications of anonymous immigration service purchases
   */
  @Post('/guest-immigration')
  @ApiOperation({
    summary: 'Process immigration service payment (Guest users - HIGH RISK)',
    description:
      'Allows unauthenticated users to purchase immigration services. Enhanced security recommended.',
  })
  async guestImmigration(@Body() dto: UserImmigrationServiceDto) {
    // CRITICAL SECURITY GAP: No verification for sensitive immigration services
    return await this.payment.guest_immigration(dto);
  }

  /**
   * Process Training Service Payment for Authenticated Users
   *
   * Handles payment processing for training programs, courses, and educational services.
   * Training services may include online courses, workshops, certifications, etc.
   *
   * @param user - Authenticated user object from JWT token
   * @param dto - Training service payment data including course/program details
   * @returns Promise<PaymentIntent> - Stripe payment intent for training service
   *
   * BUSINESS CONSIDERATIONS:
   * - Training services may have enrollment limits
   * - Consider course prerequisites and user eligibility
   * - May include recurring payments for subscription-based training
   * - Track user progress and completion for value delivery
   */
  @UseGuards(JwtGuard) // Authentication required for training enrollment
  @ApiBearerAuth()
  @Post('/training')
  @ApiOperation({
    summary: 'Process training service payment (Authenticated users only)',
    description:
      'Creates payment intent for training programs and courses. Requires authentication for enrollment tracking.',
  })
  async user_training(
    @GetUser() user: IJWTPayload,
    @Body() dto: UserTrainingServiceDto,
  ) {
    return await this.payment.user_training(user, dto);
  }

  /**
   * Process Training Service Payment for Guest Users
   *
   * Allows unauthenticated users to purchase training services.
   * Guest training purchases may have limitations compared to registered users.
   *
   * @param dto - Training service payment data with guest contact information
   * @returns Promise<PaymentIntent> - Stripe payment intent for guest training purchase
   *
   * BUSINESS LIMITATIONS:
   * - Guest users may not access full training platform features
   * - Progress tracking limited without user account
   * - Consider encouraging registration for better experience
   *
   * SECURITY: Same rate limiting concerns as other guest endpoints
   */
  @Post('/guest-training')
  @ApiOperation({
    summary: 'Process training service payment (Guest users)',
    description:
      'Allows unauthenticated users to purchase training services with limited features.',
  })
  async guestTraining(@Body() dto: UserTrainingServiceDto) {
    return await this.payment.guest_training(dto);
  }

  /**
   * Stripe Webhook Handler
   *
   * Processes webhook events from Stripe to handle payment status updates,
   * successful payments, failed payments, and other payment lifecycle events.
   * This endpoint is called by Stripe's servers, not by client applications.
   *
   * @param req - Raw Fastify request with Stripe webhook payload and headers
   * @returns Promise<void> - Acknowledgment response to Stripe
   *
   * CRITICAL SECURITY REQUIREMENTS:
   * - MUST verify Stripe webhook signature to prevent spoofing
   * - MUST whitelist Stripe IP addresses in production
   * - Raw body required for signature verification
   * - Idempotency handling for duplicate webhook deliveries
   *
   * WEBHOOK EVENTS TO HANDLE:
   * - payment_intent.succeeded: Complete order fulfillment
   * - payment_intent.payment_failed: Handle failed payments
   * - payment_intent.canceled: Clean up canceled payments
   * - invoice.payment_succeeded: For subscription payments
   *
   * OPERATIONAL REQUIREMENTS:
   * - Implement proper logging for webhook events
   * - Add monitoring and alerting for webhook failures
   * - Ensure fast response time (< 10 seconds) to avoid retries
   * - Handle webhook retries gracefully (Stripe retries failed webhooks)
   */
  @Post('/web-hook')
  @ApiOperation({
    summary: 'Stripe webhook handler',
    description:
      'Processes payment status updates from Stripe. Called by Stripe servers only.',
  })
  async webhook(@Req() req: RawBodyRequest<FastifyRequest>) {
    // SECURITY CRITICAL: Ensure webhook signature verification in service layer
    return await this.payment.webhook(req);
  }
}

/**
 * PAYMENT CONTROLLER REVIEW SUMMARY
 *
 * ARCHITECTURE STRENGTHS:
 * ✅ Good separation of concerns (controller delegates to service layer)
 * ✅ Consistent endpoint naming and structure
 * ✅ Proper use of NestJS decorators and guards
 * ✅ Swagger documentation for API endpoints
 * ✅ DTO validation for request data
 *
 * CRITICAL SECURITY ISSUES TO ADDRESS:
 * 🚨 No rate limiting on any endpoints (especially critical for guest endpoints)
 * 🚨 Guest endpoints for immigration services pose compliance risks
 * 🚨 Missing webhook signature verification documentation
 * 🚨 No IP whitelisting mentioned for webhook endpoint
 * 🚨 Missing IJWTPayload interface import
 *
 * RECOMMENDED IMPROVEMENTS:
 * 1. Implement rate limiting middleware:
 *    - Authenticated endpoints: 100 requests/hour per user
 *    - Guest endpoints: 10 requests/hour per IP
 *    - Immigration guest endpoint: 3 requests/day per IP
 *
 * 2. Add security middleware:
 *    - CAPTCHA verification for guest endpoints
 *    - Email/phone verification for immigration services
 *    - Enhanced fraud detection
 *
 * 3. Improve error handling:
 *    - Add try-catch blocks with proper error responses
 *    - Implement structured error logging
 *    - Add correlation IDs for request tracing
 *
 * 4. Add monitoring and observability:
 *    - Payment success/failure metrics
 *    - Response time monitoring
 *    - Webhook delivery monitoring
 *    - Security event alerting
 *
 * 5. Code quality improvements:
 *    - Fix missing IJWTPayload import
 *    - Add input validation middleware
 *    - Implement request/response logging
 *    - Add unit and integration tests
 *
 * 6. Business logic enhancements:
 *    - Add payment amount validation against service pricing
 *    - Implement idempotency for payment requests
 *    - Add support for payment method validation
 *    - Consider implementing payment retry logic
 *
 * COMPLIANCE CONSIDERATIONS:
 * - GDPR compliance for EU users
 * - PCI DSS compliance for payment processing
 * - Immigration law compliance for immigration services
 * - Data retention policies for payment records
 *
 * PERFORMANCE OPTIMIZATIONS:
 * - Add caching for service pricing data
 * - Implement connection pooling for database operations
 * - Add request compression for large payloads
 * - Consider implementing payment processing queues for high volume
 */
